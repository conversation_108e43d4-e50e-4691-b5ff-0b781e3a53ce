<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>浙江中医药大学信息化升级战略</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Visualization & Content Choices:
        - Executive Summary: Key points highlighted with more detail, interactive "learn more" (scroll) for value propositions. Goal: Comprehensive Overview. Method: HTML/CSS. Interaction: Click to scroll. Justification: Quick access to relevant details.
        - Smart Campus Cloud: Cards for sub-sections (Urgency, Hybrid Cloud architecture, core functions, advanced tech integration) with expanded textual content. Simple HTML/CSS diagram for Hybrid Cloud. Goal: Explain core platform in depth. Method: HTML/CSS, Chart.js (for benefits). Interaction: Expandable cards with disclosure icons. Justification: Structured detail presentation.
        - Disaster Recovery: Detailed DR strategies, backup policies, and expanded comparison table. Goal: Compare strategies comprehensively. Method: HTML/JS for table interactivity. Interaction: Click to expand rows. Justification: Clear comparison.
        - Resource Management: Expanded details on elastic allocation, specialized computing, and intelligent storage. Goal: Detail resource strategies thoroughly. Method: HTML/CSS. Interaction: Expandable blocks with disclosure icons. Justification: Visually appealing feature breakdown.
        - Roadmap: Interactive timeline with more detailed tasks and outcomes per phase. Goal: Show detailed phased plan. Method: HTML/CSS/JS. Interaction: Click phase to see details with disclosure icons. Justification: Intuitive understanding of project progression.
        - General: Unicode icons for visual cues and disclosure. All charts via Chart.js (Canvas). Textual content directly from report, expanded for richness.
        - CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->
    <style>
        :root {
            /* 科技感配色方案 */
            --primary-color: #0066ff;
            --secondary-color: #00c8ff;
            --accent-color: #00ffaa;
            --dark-bg: #0a1929;
            --medium-bg: #132f4c;
            --light-bg: #173a5e;
            --text-color: #ffffff;
            --text-secondary: #b2bac2;
            --border-color: rgba(194, 224, 255, 0.08);
        }

        body {
            font-family: 'Inter', "Microsoft YaHei", "PingFang SC", sans-serif;
            padding-top: 72px;
            background-color: #0a1929; /* 深色背景 */
            color: var(--text-color);
        }

        .section-title {
            font-size: 1.875rem;
            line-height: 2.25rem;
            font-weight: 700;
            margin-bottom: 2rem;
            padding-bottom: 0.75rem;
            border-bottom-width: 2px;
            color: var(--secondary-color);
            border-color: rgba(0, 200, 255, 0.3);
            text-shadow: 0 0 10px rgba(0, 200, 255, 0.2);
        }

        .card {
            padding: 1.5rem;
            border-radius: 0.75rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            margin-bottom: 2rem;
            transition-property: all;
            transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
            transition-duration: 300ms;
            background-color: var(--medium-bg);
            border: 1px solid var(--border-color);
        }

        .card:hover {
            box-shadow: 0 0 25px rgba(0, 200, 255, 0.15);
            transform: translateY(-2px);
        }

        .card-header {
            font-size: 1.25rem;
            line-height: 1.75rem;
            font-weight: 600;
            margin-bottom: 0.75rem;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: var(--secondary-color);
            position: relative;
            padding: 0.5rem;
            border-radius: 0.375rem;
            transition: all 0.3s ease;
        }

        .card-header:hover {
            color: var(--accent-color);
            background-color: rgba(0, 255, 170, 0.1);
            box-shadow: 0 0 10px rgba(0, 255, 170, 0.2);
        }

        /* 添加点击指示器 */
        .card-header::before {
            content: "点击展开";
            position: absolute;
            right: 40px;
            font-size: 0.75rem;
            opacity: 0;
            color: var(--accent-color);
            transition: opacity 0.3s ease;
        }

        .card-header:hover::before {
            opacity: 1;
        }

        .card-header::after {
            content: "";
            position: absolute;
            bottom: -3px;
            left: 0;
            width: 0;
            height: 2px;
            background-color: var(--accent-color);
            transition: width 0.3s ease;
        }

        .card-header:hover::after {
            width: 100%;
        }

        .card-content {
            line-height: 1.625;
            font-size: 1rem;
            color: var(--text-secondary);
        }

        .nav-link {
            padding-left: 1rem;
            padding-right: 1rem;
            padding-top: 0.5rem;
            padding-bottom: 0.5rem;
            border-radius: 0.375rem;
            transition-property: color, background-color;
            transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
            transition-duration: 200ms;
            font-weight: 500;
            color: var(--text-secondary);
            position: relative;
            overflow: hidden;
        }

        .nav-link::before {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background-color: var(--accent-color);
            transition: width 0.3s ease;
        }

        .nav-link:hover {
            color: var(--accent-color);
            background-color: rgba(0, 255, 170, 0.1);
        }

        .nav-link:hover::before {
            width: 100%;
        }

        .nav-link.active {
            color: var(--accent-color);
            background-color: rgba(0, 255, 170, 0.15);
            font-weight: 600;
        }

        .nav-link.active::before {
            width: 100%;
        }

        .timeline-item {
            position: relative;
            padding-left: 2.5rem;
            padding-top: 1.25rem;
            padding-bottom: 1.25rem;
            border-left: 2px solid var(--secondary-color);
        }

        .timeline-dot {
            position: absolute;
            width: 1.25rem;
            height: 1.25rem;
            border-radius: 9999px;
            left: -11px;
            top: 1.5rem;
            background-color: var(--secondary-color);
            border: 4px solid var(--medium-bg);
            box-shadow: 0 0 10px rgba(0, 200, 255, 0.5);
        }

        .timeline-content h3 {
            font-size: 1.125rem;
            line-height: 1.75rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: var(--secondary-color);
            padding: 0.5rem;
            border-radius: 0.375rem;
            transition: all 0.3s ease;
            position: relative;
        }

        .timeline-content h3:hover {
            color: var(--accent-color);
            background-color: rgba(0, 255, 170, 0.1);
            box-shadow: 0 0 10px rgba(0, 255, 170, 0.2);
        }

        .timeline-content h3::before {
            content: "点击查看详情";
            position: absolute;
            right: 40px;
            font-size: 0.75rem;
            opacity: 0;
            color: var(--accent-color);
            transition: opacity 0.3s ease;
        }

        .timeline-content h3:hover::before {
            opacity: 1;
        }

        .details {
            margin-top: 0.75rem;
            font-size: 0.875rem;
            line-height: 1.625;
            row-gap: 0.5rem;
            overflow: hidden;
            transition-property: all;
            transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
            transition-duration: 500ms;
            color: var(--text-secondary);
        }

        .details.hidden {
            max-height: 0;
            opacity: 0;
            margin-top: 0;
            padding-top: 0;
            padding-bottom: 0;
        }

        .details:not(.hidden) {
            max-height: 2000px; /* Adjust as needed for max content height */
            opacity: 1;
        }

        .chart-container {
            position: relative;
            width: 100%;
            max-width: 650px;
            margin-left: auto;
            margin-right: auto;
            height: 320px;
            max-height: 420px;
        }

        @media (min-width: 768px) {
            .chart-container {
                height: 380px;
            }
        }

        .table-header-cell {
            padding-left: 1.25rem;
            padding-right: 1.25rem;
            padding-top: 0.75rem;
            padding-bottom: 0.75rem;
            border-bottom-width: 2px;
            text-align: left;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            background-color: var(--dark-bg);
            border-color: var(--border-color);
            color: var(--secondary-color);
        }

        .table-body-cell {
            padding-left: 1.25rem;
            padding-right: 1.25rem;
            padding-top: 1rem;
            padding-bottom: 1rem;
            border-bottom-width: 1px;
            font-size: 0.875rem;
            background-color: var(--medium-bg);
            border-color: var(--border-color);
            color: var(--text-secondary);
        }

        .brand-text-main { /* For "浙江中医药大学" */
            color: var(--text-color);
        }

        .brand-text-secondary { /* For "信息化升级" */
            color: var(--secondary-color);
            text-shadow: 0 0 10px rgba(0, 200, 255, 0.3);
        }

        .disclosure-arrow {
            transition: all 0.3s ease-in-out;
            font-size: 1em;
            color: var(--secondary-color);
            margin-left: 8px;
            width: 20px;
            height: 20px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: rgba(0, 200, 255, 0.1);
        }

        .disclosure-arrow.expanded {
            transform: rotate(90deg);
            color: var(--accent-color);
            background-color: rgba(0, 255, 170, 0.2);
            box-shadow: 0 0 8px rgba(0, 255, 170, 0.5);
        }

        /* 移动端优化 */
        @media (max-width: 768px) {
            body {
                padding-top: 60px;
            }

            .section-title {
                font-size: 1.5rem;
                line-height: 2rem;
            }

            .card {
                padding: 1rem;
            }

            .card-header {
                font-size: 1.125rem;
                line-height: 1.75rem;
            }

            .nav-link {
                padding-top: 0.75rem;
                padding-bottom: 0.75rem;
                display: block;
            }

            .disclosure-arrow {
                width: 24px;
                height: 24px;
            }
        }
    </style>
</head>
<body>

    <nav id="navbar" class="fixed top-0 left-0 right-0 z-50" style="background-color: var(--dark-bg); box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);">
        <div class="container mx-auto px-6 py-4 flex justify-between items-center">
            <a href="#" class="text-2xl font-bold">
                <span class="brand-text-main">浙江中医药大学</span><span class="brand-text-secondary">信息化升级</span>
            </a>
            <div class="hidden md:flex space-x-2">
                <a href="#executive-summary" class="nav-link">执行摘要</a>
                <a href="#smart-cloud" class="nav-link">智慧校园云</a>
                <a href="#disaster-recovery" class="nav-link">灾难恢复</a>
                <a href="#resource-management" class="nav-link">资源管理</a>
                <a href="#roadmap" class="nav-link">实施路线图</a>
            </div>
            <button id="mobile-menu-button" class="md:hidden focus:outline-none" style="color: var(--text-color);">
                <svg class="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7"></path></svg>
            </button>
        </div>
        <div id="mobile-menu" class="hidden md:hidden px-6 pb-4 pt-2 space-y-1" style="border-top: 1px solid var(--border-color); background-color: var(--medium-bg);">
            <a href="#executive-summary" class="block nav-link">执行摘要</a>
            <a href="#smart-cloud" class="block nav-link">智慧校园云</a>
            <a href="#disaster-recovery" class="block nav-link">灾难恢复</a>
            <a href="#resource-management" class="block nav-link">资源管理</a>
            <a href="#roadmap" class="block nav-link">实施路线图</a>
        </div>
    </nav>

    <main class="container mx-auto px-6 py-10">

        <section id="executive-summary" class="mb-20 pt-16 -mt-16">
            <h2 class="section-title">I. 执行摘要：擘画浙江中医药大学数字化未来蓝图</h2>
            <p class="text-slate-600 mb-8 leading-relaxed text-lg">本部分概述了浙江中医药大学信息化升级的宏伟愿景、核心支柱以及为学校领导层带来的关键价值，为整个战略规划奠定基调。通过本次信息化升级，滨文与富春两校区将实现无缝协同，信息资源高效共享，驱动学校实现长远发展目标、激发创新活力、提升核心竞争力。这不仅是一次技术层面的革新，更是驱动学校实现长远发展目标、激发创新活力、提升核心竞争力的战略引擎。</p>

            <div class="grid md:grid-cols-2 gap-8">
                <div class="card">
                    <h3 class="card-header" onclick="toggleContent(this)"><span>🚀 数字化转型愿景</span><span class="disclosure-arrow">▸</span></h3>
                    <div class="card-content details hidden">
                        <p>致力于将浙江中医药大学（ZCMU）建设成为一所信息技术与教育、中医药科研及行政管理深度融合的领先高等学府。通过本次信息化升级，滨文与富春两校区将实现无缝协同，信息资源高效共享。</p>
                    </div>
                </div>
                <div class="card">
                    <h3 class="card-header" onclick="toggleContent(this)"><span>🏛️ 升级核心支柱</span><span class="disclosure-arrow">▸</span></h3>
                    <div class="card-content details hidden">
                        <p>本次信息化升级方案围绕三大核心支柱构建：</p>
                        <ul class="list-disc list-inside space-y-2 mt-2 pl-2">
                            <li><strong>统一的“智慧校园云”平台：</strong>构建连接两校区的数字中枢，实现信息互通与资源共享，打破校区壁垒。</li>
                            <li><strong>高效的灾难恢复与数据备份体系：</strong>建立覆盖双校区、具备高可用性的灾备系统，保障学校业务连续性与数据安全。</li>
                            <li><strong>智能的计算与存储资源管理：</strong>实现IT资源的合理配置与弹性调度，满足教学、科研及管理的多样化需求，提升资源利用效率。</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="card mt-8">
                <h3 class="card-header" onclick="toggleContent(this)"><span>🌟 为学校领导层带来的核心价值</span><span class="disclosure-arrow">▸</span></h3>
                <div class="card-content details hidden">
                    <p>本方案的实施将为学校带来多方面的显著效益：</p>
                    <ul class="list-disc list-inside space-y-2 mt-2 pl-2">
                        <li><strong>提升运营效率：</strong>通过流程优化与信息共享，显著提高行政管理与教学科研的整体效率。</li>
                        <li><strong>赋能科研创新：</strong>尤其是在中医药领域，借助人工智能（AI）与大数据等先进技术，为科研突破提供强大算力与数据支撑。</li>
                        <li><strong>优化师生体验：</strong>提供便捷、高效、个性化的信息服务，提升师生在教学、学习、研究及校园生活中的满意度。</li>
                        <li><strong>保障数据安全：</strong>构建坚实的网络安全与数据保护屏障，确保学校核心信息资产无虞。</li>
                        <li><strong>引领未来发展：</strong>使学校具备适应未来教育模式变革与技术发展的能力，保持领先地位。</li>
                    </ul>
                    <p class="mt-4 font-semibold text-blue-600">行动倡议：我们恳请学校领导层高瞻远瞩，积极采纳并推动此项具有战略意义的信息化升级规划。这不仅是对现有设施的改进，更是对浙江中医药大学未来发展的关键投资。</p>
                </div>
            </div>
        </section>

        <section id="smart-cloud" class="mb-20 pt-16 -mt-16">
            <h2 class="section-title">II. 统一基石：浙江中医药大学智慧校园云</h2>
            <p class="text-slate-600 mb-8 leading-relaxed text-lg">“智慧校园云”是本次升级的核心，旨在构建连接两校区的统一数字中枢。本部分将阐述其构建的必要性、架构设计、核心功能以及如何集成前沿技术，驱动学校智慧化发展。ZCMU智慧云将打破物理空间界限，实现数据、应用与服务的全面互通与共享，解决“一校两区”格局下的信息孤岛问题，确保两校区作为一个有机整体高效运作。</p>

            <div class="card">
                <h3 class="card-header" onclick="toggleContent(this)"><span>❗ 构建统一数字生态系统的迫切性</span><span class="disclosure-arrow">▸</span></h3>
                <div class="card-content details hidden">
                    <p>浙江中医药大学目前拥有滨文与富春两个校区，若缺乏统一的数字生态系统，可能导致资源分散、管理复杂、信息共享不畅，进而影响教学质量、科研协作乃至整体运营效率。ZCMU智慧云旨在打破物理空间的界限，实现数据、应用与服务的全面互通与共享。对学校领导层而言，一个统一的平台不仅能强化行政管理与决策的全局视野，更能确保所有师生，无论身处哪个校区，都能公平、便捷地获取所需的教育资源与服务，从而有力提升学校的整体协同效能与品牌形象。</p>
                </div>
            </div>

            <div class="card">
                <h3 class="card-header" onclick="toggleContent(this)"><span>☁️ 云架构设计：兼顾安全与弹性的混合云模式</span><span class="disclosure-arrow">▸</span></h3>
                <div class="card-content details hidden">
                    <p>建议采用<strong>混合云架构</strong>，有机结合私有云的安全可控与公有云的灵活弹性。</p>
                    <ul class="list-disc list-inside space-y-2 mt-2 pl-2">
                        <li><strong>私有云核心 (富春校区):</strong> 将富春校区作为私有云基础设施的主要承载地，建设校级数据中心。私有云将承载学校的核心业务系统、敏感数据以及需要高度定制化的应用，确保数据主权与信息安全。</li>
                        <li><strong>公有云补充:</strong> 对于非核心业务、需要快速弹性伸缩的应用（如开学季的选课系统、大型在线活动的临时资源需求），或需要利用公有云上特定先进服务（如特定领域的AI算法库、海量存储归档）的场景，可以灵活采用公有云资源。有助于优化成本，按需付费。</li>
                    </ul>
                    <p class="mt-3 font-semibold">校区整合与连接方案：</p>
                    <ul class="list-disc list-inside space-y-1 mt-1 pl-2">
                        <li><strong>富春校区：</strong>作为私有云的核心节点。</li>
                        <li><strong>滨文校区：</strong>升级网络基础设施，通过高速、冗余网络链路接入ZCMU智慧云，可部署边缘计算节点。</li>
                        <li><strong>多校区网络互联：</strong>构建高带宽、低延迟、高可靠的骨干网络，可采用裸光纤直连，并考虑引入SD-WAN技术。</li>
                    </ul>
                    <div class="mt-4 p-4 border border-cyan-200 rounded-lg bg-cyan-50">
                        <p class="font-semibold text-cyan-700">图示：混合云架构示意</p>
                        <div class="flex flex-col md:flex-row justify-around items-center mt-3 text-sm">
                            <div class="p-3 m-1 bg-white rounded-lg shadow text-center w-full md:w-auto">富春校区<br>(主数据中心 - 私有云)</div>
                            <span class="text-3xl mx-2 my-2 md:my-0 text-cyan-500 font-light">&harr;</span>
                            <div class="p-3 m-1 bg-white rounded-lg shadow text-center w-full md:w-auto">滨文校区<br>(灾备/边缘节点)</div>
                            <span class="text-3xl mx-2 my-2 md:my-0 text-cyan-500 font-light">&harr;</span>
                            <div class="p-3 m-1 bg-white rounded-lg shadow text-center w-full md:w-auto">公有云<br>(弹性资源/特定服务)</div>
                        </div>
                    </div>
                    <p class="mt-3 text-xs text-slate-500">混合云是实现信息化统一规划、提升服务效率、规范资源管理的有效途径，能够打通私有云与公有云壁垒，支持高峰值业务和低成本云灾备。</p>
                </div>
            </div>

            <div class="card">
                <h3 class="card-header" onclick="toggleContent(this)"><span>🔑 ZCMU智慧云的核心功能与价值</span><span class="disclosure-arrow">▸</span></h3>
                <div class="card-content details hidden">
                    <ul class="list-disc list-inside space-y-2 mt-2 pl-2">
                        <li><strong>统一信息访问与服务交付：</strong>建立统一身份认证与单点登录（SSO）系统。构建集中的数据中心与共享数据库。各类应用服务通过云平台统一发布。</li>
                        <li><strong>强化协作与创新：</strong>为跨校区、跨学科的教学与科研协作提供便利。共享专业软件、大型仪器设备数据、中医药特色数据库及科研计算资源。</li>
                        <li><strong>简化运营与管理：</strong>集中的IT资源管理与自动化的运维工具降低IT管理复杂度和人力成本。领导层能从统一管理视图掌握IT资源运行状态。</li>
                        <li><strong>弹性伸缩与成本优化：</strong>根据实际需求动态调整计算与存储资源，避免资源浪费，实现按需使用、按量付费，优化IT总体拥有成本（TCO）。</li>
                    </ul>
                </div>
            </div>

            <div class="card">
                <h3 class="card-header" onclick="toggleContent(this)"><span>🚀 面向未来：集成先进技术，驱动智慧升级</span><span class="disclosure-arrow">▸</span></h3>
                <div class="card-content details hidden">
                     <p>ZCMU智慧云不仅满足当前IT需求，更要具备前瞻性，支撑学校未来发展。</p>
                    <ul class="list-disc list-inside space-y-2 mt-2 pl-2">
                        <li><strong>构建AI与大数据平台:</strong> 支持中医药科研（如中药筛选、方剂优化、临床辅助诊断）、个性化教学、智能管理。内置强大的AI计算能力（包括GPU资源）和大数据处理与分析能力。积极响应并融入省级“人工智能+教育”规划。</li>
                        <li><strong>融合物联网 (IoT) 技术:</strong> 通过部署各类传感器与智能设备，并将这些设备统一接入物联网平台，实现校园环境实时感知、数据自动采集及智能化管理与服务（如智慧教室、智能楼宇）。</li>
                        <li><strong>支持5G与IPv6:</strong> 为高清视频教学、VR/AR沉浸式体验、大规模物联网设备连接提供网络保障。IPv6满足未来智慧校园中万物互联的需求。</li>
                    </ul>
                    <p class="mt-3 font-medium">ZCMU智慧云的构建，特别是对AI与大数据能力的前瞻性布局，将从IT支撑平台提升为驱动学校核心使命发展的战略资产。</p>
                </div>
            </div>
             <div class="card">
                <h3 class="card-header"><span>📊 智慧校园云效益预期 (示例图表)</span><span class="disclosure-arrow">▸</span></h3>
                <div class="chart-container mt-4">
                    <canvas id="cloudBenefitsChart"></canvas>
                </div>
                <p class="text-sm text-slate-500 mt-3 text-center">注：此图表为示例，展示智慧云平台可能带来的效益提升（如效率、资源利用率、科研支持、师生满意度等）。实际指标需进一步调研确定。</p>
            </div>
        </section>

        <section id="disaster-recovery" class="mb-20 pt-16 -mt-16">
            <h2 class="section-title">III. 保障韧性：构建先进的灾难恢复与数据备份体系</h2>
            <p class="text-slate-600 mb-8 leading-relaxed text-lg">数据是学校核心战略资产，其完整性、可用性和安全性直接关系到学校的正常运转和声誉。建立强大可靠的灾难恢复（DR）与数据备份体系至关重要。对于一所医学院校，数据的安全与保密性在灾备体系建设中尤为重要，必须确保符合国家及行业相关规定。</p>

            <div class="card">
                <h3 class="card-header" onclick="toggleContent(this)"><span>🛡️ 双校区灾难恢复（DR）与备份策略</span><span class="disclosure-arrow">▸</span></h3>
                <div class="card-content details hidden">
                    <p>以富春校区为主数据中心，滨文校区为灾备数据中心。基于业务系统重要性分级：</p>
                    <ul class="list-disc list-inside space-y-2 mt-2 pl-2">
                        <li><strong>核心关键业务系统 (如学籍管理、财务系统、核心科研数据库等):</strong> 考虑部署两校区数据中心间的应用级双活或多活容灾方案 (RPO≈0, RTO极低)。</li>
                        <li><strong>重要业务系统 (如图书馆系统、普通教学应用等):</strong> 采用主备（Active-Standby）容灾模式 (秒级RPO, 分钟/小时级RTO)。</li>
                        <li><strong>一般业务系统及数据归档:</strong> 考虑采用基于云的灾难恢复服务（Cloud DRaaS），经济高效。</li>
                    </ul>
                    <p class="mt-3 font-semibold">数据备份策略要点：</p>
                     <ul class="list-disc list-inside space-y-1 mt-1 pl-2">
                        <li>全面覆盖与自动化。</li>
                        <li>遵循3-2-1备份原则（三副本、两介质、一异地）。</li>
                        <li>分层存储与生命周期管理。</li>
                        <li>采用不可变备份与勒索病毒防护技术。</li>
                        <li>定期演练与验证。</li>
                    </ul>
                    <p class="mt-3 text-xs text-slate-500">领导层应参与关键业务评估及RTO/RPO定义，以实现差异化灾备策略，优化资源配置。</p>
                </div>
            </div>
             <div class="card">
                <h3 class="card-header" onclick="toggleContent(this)"><span>⚙️ 关键技术与架构考量</span><span class="disclosure-arrow">▸</span></h3>
                <div class="card-content details hidden">
                    <ul class="list-disc list-inside space-y-2 mt-2 pl-2">
                        <li><strong>数据复制技术：</strong>根据RPO要求选择同步或异步复制。</li>
                        <li><strong>虚拟化技术：</strong>广泛采用服务器虚拟化，简化灾难恢复流程，缩短RTO。</li>
                        <li><strong>网络冗余与安全：</strong>确保主备数据中心之间及与云平台间有多条冗余网络链路，并实施同等级别安全防护，数据传输和存储加密。</li>
                        <li><strong>自动化灾备管理平台：</strong>实现流程自动化编排、一键式切换/回切、状态监控和演练管理。</li>
                    </ul>
                </div>
            </div>

            <div class="card overflow-x-auto">
                <h3 class="card-header"><span>📋 浙江中医药大学灾难恢复策略比较表</span><span class="disclosure-arrow">▸</span></h3>
                <div class="card-content details hidden">
                    <table class="min-w-full divide-y divide-gray-200 mt-2">
                        <thead>
                            <tr>
                                <th class="table-header-cell">策略方案</th>
                                <th class="table-header-cell">核心特征</th>
                                <th class="table-header-cell">RPO</th>
                                <th class="table-header-cell">RTO</th>
                                <th class="table-header-cell">优势</th>
                                <th class="table-header-cell">挑战/考量</th>
                                <th class="table-header-cell">相对成本</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200 text-sm">
                            <tr>
                                <td class="table-body-cell font-medium text-blue-700">校区间应用双活</td>
                                <td class="table-body-cell">两校区同时运行，实时同步，自动切换</td>
                                <td class="table-body-cell">接近零</td>
                                <td class="table-body-cell">秒级至分钟级</td>
                                <td class="table-body-cell">最高连续性, 用户无感知切换, 充分利用双校区资源</td>
                                <td class="table-body-cell">成本最高, 技术复杂, 网络要求苛刻 (如&lt;3ms延迟), 需应用改造</td>
                                <td class="table-body-cell text-red-600 font-semibold">高</td>
                            </tr>
                            <tr>
                                <td class="table-body-cell font-medium text-blue-700">校区间主备容灾</td>
                                <td class="table-body-cell">一主一备，数据异步/同步复制，故障时切换</td>
                                <td class="table-body-cell">秒级至分钟级</td>
                                <td class="table-body-cell">分钟级至小时级</td>
                                <td class="table-body-cell">较好连续性, 技术成熟, 成本相对较低</td>
                                <td class="table-body-cell">少量数据丢失风险, 备用资源可能闲置, 切换可能短暂中断</td>
                                <td class="table-body-cell text-yellow-600 font-semibold">中</td>
                            </tr>
                            <tr>
                                <td class="table-body-cell font-medium text-blue-700">云灾备服务(DRaaS)</td>
                                <td class="table-body-cell">业务/数据备份至公有云，按需恢复</td>
                                <td class="table-body-cell">分钟级至小时级</td>
                                <td class="table-body-cell">小时级</td>
                                <td class="table-body-cell">成本效益高, 弹性伸缩, 无需自建硬件, 部署快速</td>
                                <td class="table-body-cell">恢复时间可能较长, 依赖互联网与云服务商, 数据主权与合规性评估</td>
                                <td class="table-body-cell text-green-600 font-semibold">低至中</td>
                            </tr>
                        </tbody>
                    </table>
                     <p class="mt-4 text-sm text-slate-500">学校应根据各业务系统的实际需求，组合采用上述策略，构建多层次、立体化的灾备防护体系。所有敏感数据在传输和存储时必须加密，并实施严格的权限控制和审计。</p>
                </div>
            </div>
        </section>

        <section id="resource-management" class="mb-20 pt-16 -mt-16">
            <h2 class="section-title">IV. 敏捷资源：实现计算与存储的智能管理</h2>
            <p class="text-slate-600 mb-8 leading-relaxed text-lg">高校IT资源需求呈动态性和周期性特征。传统固定资源分配模式难以适应，易导致资源浪费或不足。通过ZCMU智慧云实现计算（CPU、GPU）和存储资源的合理化管理与弹性化分配，是提升IT服务效能、支持学校创新发展的关键。技术与管理双管齐下，才能真正实现“合理管理及弹性分配”的目标。</p>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="card">
                    <h3 class="card-header" onclick="toggleContent(this)"><span>💡 弹性资源分配策略</span><span class="disclosure-arrow">▸</span></h3>
                    <div class="card-content details hidden">
                        <ul class="list-disc list-inside space-y-2 mt-2 pl-2">
                            <li><strong>深度虚拟化与容器化：</strong>全面推广服务器虚拟化，积极引入容器化技术（Docker, Kubernetes）实现应用快速部署、轻量化运行。</li>
                            <li><strong>构建自服务门户：</strong>为院系、科研团队及学生提供统一资源申请与管理门户，审批流程在线化、自动化。</li>
                            <li><strong>统一资源池管理：</strong>将两校区计算和存储资源纳入统一云管理平台集中调度分配，打破部门壁垒。</li>
                            <li><strong>自动化编排与智能调度：</strong>利用云管理平台自动化编排工具，根据预设策略自动完成资源部署、配置、监控和回收。</li>
                        </ul>
                    </div>
                </div>
                <div class="card">
                    <h3 class="card-header" onclick="toggleContent(this)"><span>🔬 专业化计算需求支持</span><span class="disclosure-arrow">▸</span></h3>
                    <div class="card-content details hidden">
                        <ul class="list-disc list-inside space-y-2 mt-2 pl-2">
                            <li><strong>高性能计算 (HPC) 与GPU资源池：</strong>规划建设或整合接入HPC集群和GPU资源池，通过云平台提供弹性的、按需的GPU算力服务，支持生命科学、中医药研发等。</li>
                            <li><strong>虚拟桌面基础架构 (VDI) / 桌面即服务 (DaaS)：</strong>为师生提供标准化虚拟桌面环境，预装各类软件，支持随时随地安全访问，适用于计算机实验室、远程办公学习。</li>
                        </ul>
                         <p class="mt-3 font-medium">为中医药AI研究和人才培养提供便捷的GPU资源，将极大提升学校创新能力和人才培养质量，形成独特竞争优势。</p>
                    </div>
                </div>
                <div class="card">
                    <h3 class="card-header" onclick="toggleContent(this)"><span>🗄️ 智能化存储管理体系</span><span class="disclosure-arrow">▸</span></h3>
                    <div class="card-content details hidden">
                        <ul class="list-disc list-inside space-y-2 mt-2 pl-2">
                            <li><strong>分层存储架构：</strong>根据数据访问频率、重要性构建多层级存储体系（高速SSD、大容量SATA、磁带库/对象存储），通过智能数据分层技术自动迁移数据，平衡成本与性能。</li>
                            <li><strong>精细化数据生命周期管理：</strong>制定明确数据管理策略，涵盖数据创建、存储、使用、共享、归档和销毁全过程，确保合规并控制存储增长。</li>
                            <li><strong>个性化网络存储空间：</strong>为每位师生提供安全、便捷、容量充足的个人网络存储空间（云盘），支持多终端同步与共享。</li>
                        </ul>
                    </div>
                </div>
            </div>
             <div class="card mt-8">
                <h3 class="card-header" onclick="toggleContent(this)"><span>📈 为ZCMU带来的核心效益</span><span class="disclosure-arrow">▸</span></h3>
                <div class="card-content details hidden">
                    <ul class="list-disc list-inside space-y-2 mt-2 pl-2">
                        <li><strong>显著的成本效益:</strong> 避免硬件过度采购和长期闲置，降低能耗和运维成本。</li>
                        <li><strong>提升的敏捷性与响应速度:</strong> 快速响应IT资源新需求，几分钟内完成新服务环境部署，缩短科研周期。</li>
                        <li><strong>强化的创新支撑能力:</strong> 提供先进计算工具和数据平台，激发创新活力，特别是在中医药与现代科技交叉融合领域。</li>
                        <li><strong>均等化的资源获取:</strong> 确保两校区师生公平、便捷地获得所需计算与存储资源，促进教育公平。</li>
                    </ul>
                    <p class="mt-3 text-xs text-slate-500">推行弹性资源管理需配套管理制度和治理框架，包括资源申请审批、使用规范、配额管理及成本分摊机制。</p>
                </div>
            </div>
        </section>

        <section id="roadmap" class="pt-16 -mt-16">
            <h2 class="section-title">V. 战略实施路线图与未来展望</h2>
            <p class="text-slate-600 mb-8 leading-relaxed text-lg">信息化升级是一项系统工程，建议采取分阶段、迭代推进的策略，确保项目顺利实施并有效控制风险。每个阶段的成功都为下一阶段积累经验和信心，使得预算投入更加精准有效。</p>

            <div class="card">
                <h3 class="card-header"><span>🗺️ 分阶段实施方略：稳步推进，持续优化</span><span class="disclosure-arrow">▸</span></h3>
                <div class="card-content mt-4 details hidden">
                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                            <h3 onclick="toggleDetails(this)"><span>第一阶段：奠定坚实基础 (预计12-18个月)</span><span class="disclosure-arrow">▸</span></h3>
                            <div class="details hidden">
                                <p><strong>核心任务:</strong> 完成ZCMU智慧云的骨干网络建设与核心基础设施部署。</p>
                                <p class="font-semibold mt-1">具体内容:</p>
                                <ul class="list-disc list-inside pl-4 text-xs">
                                    <li>建成连接滨文与富春校区的高速、冗余骨干网络。</li>
                                    <li>在富春校区建成主数据中心，部署私有云平台核心资源。</li>
                                    <li>在滨文校区初步建立灾备节点，实现最关键业务的跨校区灾备。</li>
                                    <li>完成统一身份认证平台建设，迁移校级基础服务至新云平台。</li>
                                </ul>
                                <p class="font-semibold mt-1">预期成果:</p>
                                 <p class="text-xs">两校区网络初步联通，核心业务系统上云并具备基本灾备能力，为后续智慧应用打下基础。</p>
                            </div>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                            <h3 onclick="toggleDetails(this)"><span>第二阶段：拓展服务能力，深化应用融合 (预计后续12-24个月)</span><span class="disclosure-arrow">▸</span></h3>
                            <div class="details hidden">
                                <p><strong>核心任务:</strong> 全面推广云服务，整合现有应用，初步构建智慧校园应用生态。</p>
                                 <p class="font-semibold mt-1">具体内容:</p>
                                <ul class="list-disc list-inside pl-4 text-xs">
                                    <li>全面推广VDI、统一通信、云盘等基础云服务。</li>
                                    <li>通过自服务门户向院系和科研团队提供弹性计算与存储资源。</li>
                                    <li>逐步将各部门现有应用迁移至云平台或进行云化改造。</li>
                                    <li>初步建成校级大数据平台和AI计算平台，支持中医药特色科研与教学创新应用试点。</li>
                                    <li>扩展物联网平台能力，在智慧教室、智慧安防等领域开展示范应用。</li>
                                </ul>
                                <p class="font-semibold mt-1">预期成果:</p>
                                <p class="text-xs">云服务覆盖面显著扩大，师生普遍享受到便捷的云资源，数据孤岛初步打通，智慧应用开始赋能教学科研。</p>
                            </div>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                            <h3 onclick="toggleDetails(this)"><span>第三阶段：持续优化创新，引领智慧未来 (长期进行)</span><span class="disclosure-arrow">▸</span></h3>
                            <div class="details hidden">
                                <p><strong>核心任务:</strong> 不断优化云平台性能与成本效益，持续探索和引入前沿技术，深化智慧校园建设内涵。</p>
                                <p class="font-semibold mt-1">具体内容:</p>
                                <ul class="list-disc list-inside pl-4 text-xs">
                                    <li>基于运行数据，持续优化资源调度、存储分层策略，提升资源利用率，降低运营成本。</li>
                                    <li>密切关注AI、大数据、边缘计算等技术发展趋势，适时引入并应用于中医药研究、智慧医疗等领域。</li>
                                    <li>完善数据治理体系，深化数据分析与挖掘，为学校管理决策提供更精准的智能支持。</li>
                                    <li>根据师生反馈和业务发展需求，不断迭代和创新智慧应用与服务。</li>
                                </ul>
                                <p class="font-semibold mt-1">预期成果:</p>
                                <p class="text-xs">ZCMU智慧云成为一个持续进化、高度智能、深度融合业务的数字底座，有力支撑学校建设成为国内一流、国际上有影响的中医药大学。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <h3 class="card-header" onclick="toggleContent(this)"><span>🔑 成功部署的关键保障因素</span><span class="disclosure-arrow">▸</span></h3>
                <div class="card-content details hidden">
                    <ul class="list-disc list-inside space-y-2 mt-2 pl-2">
                        <li><strong>强有力的治理架构与领导层支持：</strong>成立由校领导牵头的项目领导小组与工作小组，明确职责，建立高效决策与协调机制。</li>
                        <li><strong>科学的变革管理与用户赋能：</strong>制定详尽变革管理计划，通过宣传、培训、试点引导提升师生及管理人员数字素养和新系统应用能力。</li>
                        <li><strong>审慎的合作伙伴选择：</strong>综合考量其在高等教育领域（尤其医学院校）的成功案例、技术实力、服务能力及与学校理念的契合度。优先选择支持“异构兼容”的厂商。</li>
                        <li><strong>贯穿始终的安全理念：</strong>严格遵循法律法规，参照等级保护2.0等标准，将“安全内建”理念融入全过程，构建主动防御、纵深防护的安全保障体系。</li>
                        <li><strong>可持续的预算投入与运营机制：</strong>制定科学预算规划，探索建立与服务成效挂钩的IT资源成本核算与分担机制，确保信息化体系可持续发展。</li>
                    </ul>
                </div>
            </div>

            <div class="card">
                <h3 class="card-header" onclick="toggleContent(this)"><span>🌟 终极愿景：数字化赋能的行业领导者</span><span class="disclosure-arrow">▸</span></h3>
                <div class="card-content details hidden">
                    <p>展望未来5至10年，通过本次信息化升级的成功实施，浙江中医药大学将展现出全新的面貌：</p>
                    <ul class="list-disc list-inside space-y-1 mt-2 pl-2 text-sm">
                        <li>智慧教学深度普及，个性化学习精准推送，VR/AR带来沉浸式中医药教学体验。</li>
                        <li>科研创新成果丰硕，基于大数据与AI的中医药研究取得突破，学校成为中医药智能研究高地。</li>
                        <li>校园管理高效智能，一站式服务通达两校区，决策更加科学精准，师生体验更便捷舒适。</li>
                        <li>文化传承与创新交相辉映，数字技术赋能中医药文化的保护、传承与国际传播。</li>
                    </ul>
                    <p class="mt-3 font-semibold">滨文校区的历史底蕴与富春校区的现代活力，将在统一的智慧校园云平台上交融辉映，共同谱写浙江中医药大学发展的新华章。此次信息化升级，必将为学校插上腾飞的翅膀，使其成为一所真正意义上由数字化全面赋能、在中医药高等教育领域具有引领示范作用的卓越学府。</p>
                </div>
            </div>
        </section>
    </main>

    <footer style="background-color: var(--dark-bg); color: var(--text-secondary); padding: 2.5rem 0; text-align: center; margin-top: 3rem; border-top: 1px solid var(--border-color);">
        <p class="text-sm">&copy; 浙江中医药大学 信息化升级战略规划 (交互式概览)</p>
        <p class="text-xs mt-1" style="color: var(--secondary-color);">Zhejiang Chinese Medical University - Information Technology Upgrade Strategy</p>
    </footer>

    <script>
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileMenu = document.getElementById('mobile-menu');
        mobileMenuButton.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });

        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    const navbarHeight = document.getElementById('navbar').offsetHeight;
                    const elementPosition = targetElement.getBoundingClientRect().top;
                    const offsetPosition = elementPosition + window.pageYOffset - navbarHeight;

                    window.scrollTo({
                        top: offsetPosition,
                        behavior: 'smooth'
                    });
                }
                if (!mobileMenu.classList.contains('hidden')) {
                    mobileMenu.classList.add('hidden');
                }
            });
        });

        const sections = document.querySelectorAll('section');
        const navLinks = document.querySelectorAll('nav a.nav-link');
        const navbarHeight = document.getElementById('navbar') ? document.getElementById('navbar').offsetHeight : 72;

        window.addEventListener('scroll', () => {
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop - navbarHeight - 70; // Increased offset for better accuracy
                if (pageYOffset >= sectionTop) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href').substring(1) === current) {
                    link.classList.add('active');
                }
            });
        });

        function updateArrow(arrowElement, isExpanded) {
            if (arrowElement) {
                arrowElement.textContent = isExpanded ? '▾' : '▸';
                arrowElement.classList.toggle('expanded', isExpanded);

                // Add a subtle pulse animation when toggling
                arrowElement.style.animation = 'none';
                setTimeout(() => {
                    arrowElement.style.animation = 'pulse 0.5s ease-in-out';
                }, 10);
            }
        }

        // Add pulse animation keyframes
        const style = document.createElement('style');
        style.textContent = `
            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.3); }
                100% { transform: scale(1); }
            }
        `;
        document.head.appendChild(style);

        function toggleContent(headerElement) {
            const contentElement = headerElement.nextElementSibling;
            const arrowElement = headerElement.querySelector('.disclosure-arrow');
            if (contentElement && contentElement.classList.contains('details')) {
                const isHidden = contentElement.classList.contains('hidden');
                contentElement.classList.toggle('hidden');
                updateArrow(arrowElement, !isHidden);
            }
        }

        function toggleDetails(headerElement) { // For timeline items
            const detailsElement = headerElement.nextElementSibling;
            const arrowElement = headerElement.querySelector('.disclosure-arrow');
            if (detailsElement && detailsElement.classList.contains('details')) {
                 const isHidden = detailsElement.classList.contains('hidden');
                detailsElement.classList.toggle('hidden');
                updateArrow(arrowElement, !isHidden);
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            // Initialize all details sections to be hidden and arrows to be '▸'
            document.querySelectorAll('.card-content.details, .timeline-content .details').forEach(content => {
                 content.classList.add('hidden');
                 const header = content.previousElementSibling;
                 if (header) {
                    const arrow = header.querySelector('.disclosure-arrow');
                    if (arrow) {
                        updateArrow(arrow, false);
                    }
                 }
            });

            // Open specific cards by default
            const execSummaryFirstCard = document.querySelector('#executive-summary .card .card-header');
            if (execSummaryFirstCard) {
                toggleContent(execSummaryFirstCard); // This will open and set arrow
            }

            const hybridCloudCardHeader = Array.from(document.querySelectorAll('#smart-cloud .card .card-header')).find(el => el.textContent.includes('云架构设计'));
            if (hybridCloudCardHeader) {
                toggleContent(hybridCloudCardHeader); // This will open and set arrow
            }

            const ctxCloudBenefits = document.getElementById('cloudBenefitsChart');
            if (ctxCloudBenefits) {
                new Chart(ctxCloudBenefits, {
                    type: 'bar',
                    data: {
                        labels: ['运营效率提升', '资源利用率', '科研支持强化', '师生满意度'],
                        datasets: [{
                            label: '预期效益提升 (%)',
                            data: [35, 50, 40, 30],
                            backgroundColor: [
                                'rgba(0, 102, 255, 0.6)',  // primary-color
                                'rgba(0, 200, 255, 0.6)',  // secondary-color
                                'rgba(0, 255, 170, 0.6)',  // accent-color
                                'rgba(0, 200, 255, 0.4)'   // secondary-color lighter
                            ],
                            borderColor: [
                                'rgba(0, 102, 255, 1)',    // primary-color
                                'rgba(0, 200, 255, 1)',    // secondary-color
                                'rgba(0, 255, 170, 1)',    // accent-color
                                'rgba(0, 200, 255, 0.8)'   // secondary-color
                            ],
                            borderWidth: 1.5,
                            borderRadius: 5,
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                suggestedMax: 60,
                                ticks: { color: '#b2bac2', font: { weight: '500'} }, // text-secondary
                                grid: { color: 'rgba(194, 224, 255, 0.1)' }     // border-color but more visible
                            },
                            x: {
                                ticks: { color: '#b2bac2', font: { weight: '500'} },
                                grid: { display: false }
                            }
                        },
                        plugins: {
                            legend: {
                                display: false
                            },
                            tooltip: {
                                backgroundColor: '#0a1929', // dark-bg
                                titleColor: '#00c8ff',      // secondary-color
                                bodyColor: '#ffffff',       // text-color
                                padding: 10,
                                cornerRadius: 4,
                                titleFont: { weight: 'bold'},
                                callbacks: {
                                    label: function(context) {
                                        let label = context.dataset.label || '';
                                        if (label) {
                                            label += ': ';
                                        }
                                        if (context.parsed.y !== null) {
                                            label += context.parsed.y + '%';
                                        }
                                        return label;
                                    }
                                }
                            }
                        }
                    }
                });
            }
        });
    </script>
</body>
</html>
